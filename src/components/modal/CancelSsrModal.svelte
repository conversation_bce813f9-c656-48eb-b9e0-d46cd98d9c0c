<script>
  import {
    ComposedModal,
    <PERSON>dal<PERSON><PERSON>er,
    <PERSON>dal<PERSON>ody,
    Modal<PERSON>ooter,
    InlineLoading,
    DataTable,
    Checkbox,
  } from 'carbon-components-svelte';
  import { _ } from 'src/lib/i18n.js';
  import { PrimaryButton } from 'src/components/common/button';
  import { EMPTY, APP_EVENT_NAME, CODE_SUCCESS } from 'src/constants/app.js';
  import { postOrderChange } from 'src/service/reservation';
  import { message } from 'src/store';
  import appEvent from 'src/store/appEventStore';
  import 'src/styles/modal/cancel-ssr.scss';

  export let open = false;
  export let dataSsrCancel = [];
  const hash = window.location.hash;
  const urlParams = new URLSearchParams(hash.substring(2));
  const orderId = urlParams.get('orderId');
  const headers = [
    {
      key: 'Action',
      value: '선택',
      width: '5%',
    },
    {
      key: 'airlineID',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.airlineID', {
        default: '항공사',
      }),
      width: '12%',
    },
    {
      key: 'givenNameAndPtc',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.givenNameAndPtc', {
        default: '탑승객명(PTC)',
      }),
      width: '20%',
    },
    {
      key: 'name',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.name', {
        default: 'SSR',
      }),
      width: '20%',
    },
    {
      key: 'status',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.status', {
        default: '상태',
      }),
      width: '8%',
    },
    {
      key: 'segments',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.flightNumberAirportCodeDate', {
        default: 'Segments',
      }),
      width: '20%',
    },
  ];
  let selectedRowIds = [];
  let isLoading = false;

  function closeModal() {
    open = false;
  }
  function handleSelectRowTable(e, row) {
    const status = e.detail;
    const id = row.id;
    if (status) {
      if (selectedRowIds.length <= dataSsrCancel.length - 1) {
        selectedRowIds = [...selectedRowIds, id];
      }
    } else {
      selectedRowIds = selectedRowIds.filter((selected) => selected !== id);
    }
  }

  async function submitCancelSsr() {
    isLoading = true;
    console.log(selectedRowIds, dataSsrCancel);
    const payloadCancelPnr = {
      query: {
        OrderID: orderId,
        ChangeOrderChoice: {
          CancelUnpaidOrder: {
            OrderRefID: orderId,
            Owner: dataSsrCancel[0]?.airlineID ?? EMPTY,
            SelectedOrderItemRefID:
              dataSsrCancel?.filter((elm) => selectedRowIds.includes(elm.id)).map((elm) => elm.OrderItemID) ?? [],
          },
        },
      },
    };
    const { code, data } = await postOrderChange(payloadCancelPnr);
    if (code === 200 && data.ResultMessage.Code !== CODE_SUCCESS) {
      const errorMessage = data?.ResultMessage?.Message || 'Error';
      message.set([
        {
          type: 'error',
          title: 'Error',
          subtitle: errorMessage,
        },
      ]);
      return;
    }

    message.set([
      {
        type: 'success',
        title: 'Success',
        subtitle: 'SSR 정상적으로 취소 처리되었습니다',
      },
    ]);
    appEvent.action(`${APP_EVENT_NAME.RELOAD_CURRENT_PNR_CANCELSSR}${orderId}`, {
      detail: { resOrderChange: EMPTY, endMessage: EMPTY },
    });
    try {
    } catch (error) {
      console.error(error);
    } finally {
      isLoading = false;
    }
  }
</script>

<ComposedModal bind:open size="lg" id="cancel-ssr-modal">
  <ModalHeader>
    <h4>취소할 서비스 선택</h4>
  </ModalHeader>
  <ModalBody>
    <div class="description">
      <div>취소할 무료 부가서비스를 선택해 주세요.</div>
      <div>선택한 서비스는 취소하더라도 필요 시 다시 구매하실 수 있습니다.</div>
    </div>

    <div>
      <DataTable bind:selectedRowIds {headers} rows={dataSsrCancel}>
        <div slot="cell" let:cell let:row>
          {#if cell.key === 'Action'}
            <div>
              <Checkbox class="checkbox" on:check={(e) => handleSelectRowTable(e, row)} />
            </div>
          {:else}
            {cell.value}
          {/if}
        </div>
      </DataTable>
    </div>
  </ModalBody>
  <ModalFooter>
    <footer>
      <PrimaryButton kind="secondary" width="100%" on:click={closeModal}>닫기</PrimaryButton>
      <PrimaryButton width="100%" kind="danger" disabled={selectedRowIds.length === 0} on:click={submitCancelSsr}>
        {#if isLoading}
          <div class="g-custom-loading-button loading-box">
            <InlineLoading description="Loading ..." />
          </div>
        {/if} 서비스 취소
      </PrimaryButton>
    </footer>
  </ModalFooter>
</ComposedModal>
