<script>
  import { PrimaryButton } from 'src/components/common/button';
  import { createEventDispatcher } from 'svelte';
  export let dialog;
  const dispatch = createEventDispatcher();
</script>

<dialog bind:this={dialog} on:close>
  <div class="dialog-container">
    <div class="dialog-content">
      <h3 class="dialog-title">부가서비스 추가 확인 요청</h3>
      <br />
      <p class="dialog-content-text">
        다음에 다시 부가서비스를 추가 하게 되면,<br />
        기존에 담아 놓은 서비스는 사라지고 <br />
        새로 추가한 서비스로 변경되니 주의해 주세요.
      </p>
    </div>
    <div class="dialog-footer-actions">
      
      <PrimaryButton kind="secondary" style="flex: 1; min-height: 40px" on:click={() => dialog.close()}>
        취소
      </PrimaryButton>
      <PrimaryButton
        kind="primary"
        style="flex: 1; min-height: 40px"
        on:click={() => {
          dialog.close();
          dispatch('confirm-add-service');
        }}>
        부가서비스 추가
      </PrimaryButton>
    </div>
  </div>
</dialog>

<style>
  dialog {
    min-width: 32em;
    border-radius: 0.2em;
    border: none;
    padding: 0;
    background: var(--cds-ui-01, #f4f4f4);
  }
  dialog::backdrop {
    background: rgba(0, 0, 0, 0.3);
  }

  dialog[open] {
    animation: zoom 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .dialog-title {
    font-size: 1.25rem;
  }

  .dialog-content {
    padding: 1rem 1rem 0 1rem;
  }

  .dialog-content-text {
    padding-right: 0;
    margin-bottom: 3rem;
    font-size: 0.875rem;
  }

  .dialog-footer-actions {
    display: flex;
    width: 100%;
  }
  @keyframes zoom {
    from {
      transform: scale(0.95);
    }
    to {
      transform: scale(1);
    }
  }
  dialog[open]::backdrop {
    animation: fade 0.2s ease-out;
  }
  @keyframes fade {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
</style>
