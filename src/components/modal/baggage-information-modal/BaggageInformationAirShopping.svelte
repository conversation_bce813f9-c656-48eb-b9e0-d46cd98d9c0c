<script>
  import { onMount, createEventDispatcher } from 'svelte';
  import {
    ComboBox,
    ComposedModal,
    DataTable,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Tabs,
    Tab,
  } from 'carbon-components-svelte';
  import { TrashCan } from 'carbon-icons-svelte/lib/index';
  import AddAlt from 'carbon-icons-svelte/lib/AddAlt.svelte';
  import SubtractAlt from 'carbon-icons-svelte/lib/SubtractAlt.svelte';
  import { v4 as uuidv4 } from 'uuid';
  import { isEmpty, compact, difference, isEqual, has } from 'lodash';
  import { CODE_SUCCESS, EMPTY } from 'src/constants/app';
  import Portal from 'src/components/common/Portal.svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import { getOfferPrice } from 'src/service/reservationTicketing';
  import { formatPriceNumber, sortPaxListByPtc } from 'src/utils/appHelper.js';
  import {
    PRIME_BOOKING_SERVICE,
    PRIME_BOOKING_SERVICE_HAS_MULTIPLE,
    PRIME_BOOKING_SERVICE_TYPE,
    SERVICE_NAME,
    PRIME_BOOKING_SERVICE_RECALCULATE_WHITE_LIST,
    PRIME_BOOKING_SERVICE_NOT_RECALCULATE_WHITE_LIST,
  } from 'src/constants/baggageService';
  import 'src/styles/modal/baggage-information.scss';
  import AddOnServiceConfirmRequest from 'src/components/modal/baggage-information-modal/AddOnServiceConfirmRequest.svelte';

  export let open;
  export let orderId = EMPTY;
  export let airlineID = EMPTY;
  export let orderRetrieve;
  export let serviceList;
  export let idBox = EMPTY;

  let baggageTableHeaders = [
    { key: 'ptc', value: '탑승객명 (PTC)', width: '25%' },
    { key: 'serviceType', value: '서비스선택', width: '25%' },
    { key: 'serviceAmount', value: '서비스/금액', width: '50%' },
  ];

  const dispatch = createEventDispatcher();

  let loading = 'NONE';
  let totalAmountServices = 0;
  let curCode = EMPTY;
  let headerTabs = [];
  let ptcList = [];
  let selectedTab = 0;
  let selectedServices = [];
  let dataOfferPrice = {};
  let isHiddenServiceType = false;
  let dialog;

  function handleCloseModal() {
    open = false;
  }

  function changeServiceType(e, indexRow, cell) {
    const tab = headerTabs[selectedTab];
    const { paxId } = ptcList[selectedTab][indexRow];
    const selectedValue = e.detail?.selectedId;
    ptcList[selectedTab][indexRow][cell.key] = selectedValue;
    ptcList[selectedTab][indexRow].serviceAmount = EMPTY;

    const selectedServiceType = e.detail?.selectedItem?.value;

    if (selectedServiceType) {
      const filteredServiceByPax = tab.serviceAmountOptions[selectedServiceType].filter((el) =>
        el.PaxRefID.includes(paxId)
      );

      const filteredSeatServiceBySelected = filteredServiceByPax.map((el) => {
        if (isEmpty(selectedServices)) el.disabled = false;
        return el;
      });
      ptcList[selectedTab][indexRow].serviceAmountOptions = filteredSeatServiceBySelected;
    }
  }

  function transformServiceAmountOptions(options) {
    return options.map((item, i) => {
      const combinedTxt = [
        item.Service[0].Definition.Name,
        formatPriceNumber(item.UnitPriceDetail.TotalAmount.Amount) + ' ' + item.UnitPriceDetail.TotalAmount.CurCode,
      ];
      return {
        id: i,
        text: combinedTxt.join(' / '),
        name: item.Service[0].Definition.Name,
        amount: item.UnitPriceDetail.TotalAmount.Amount,
        curCode: item.UnitPriceDetail.TotalAmount.CurCode,
        ...item,
      };
    });
  }

  function clearServiceType(indexRow) {
    ptcList[selectedTab][indexRow].serviceAmountOptions = [];
    ptcList[selectedTab][indexRow].serviceAmount = EMPTY;
  }

  function getServiceOffersRecalculate() {
    const { PaxList = [] } = orderRetrieve?.data?.DataLists;
    const { ResponseID } = serviceList?.data;
    const { OfferID, Owner } = serviceList?.data?.AlaCarteOffer;
    const paxListPayload = PaxList.map(({ LoyaltyProgramAccount, PaxID, Ptc }) => ({
      LoyaltyProgramAccount,
      PaxID,
      Ptc,
    }));
    const mappedServices = selectedServices.map((el) => {
      const nameService = el?.Service?.[0]?.Definition?.Name || EMPTY;
      const bookingInstructions = el.Service?.[0]?.Definition?.BookingInstructions;
      if (PRIME_BOOKING_SERVICE_HAS_MULTIPLE.includes(nameService) && bookingInstructions) {
        bookingInstructions.Text[0] = `TTL${el.count}KG`;
        bookingInstructions.Ositext = [bookingInstructions.Method];
      }
      return el;
    });

    const OfferItems = compact(mappedServices).map((el) => {
      const item = { OfferItemID: el.OfferItemID, PaxRefID: el.PaxRefID };
      const bookingInstructions = el.Service?.[0]?.Definition?.BookingInstructions;
      if (bookingInstructions) item.BookingInstructions = bookingInstructions;
      return item;
    });

    return { OfferItems, ResponseID, OfferID, Owner, paxListPayload };
  }

  function getBaseOffers() {
    // Prime booking orderRetrieve is OfferPriceRS
    const OfferPriceRS = orderRetrieve;
    const PricedOffer = OfferPriceRS?.data?.PricedOffer || {};
    const { OfferID = EMPTY, Owner = EMPTY, ResponseID = EMPTY, OfferItem = [] } = PricedOffer;

    return {
      OfferID,
      Owner,
      ResponseID,
      OfferItems: OfferItem?.map((offer) => ({
        OfferItemID: offer.OfferItemID,
        PaxRefID: offer.PaxRefID,
      })),
    };
  }

  function getTotalAmountServices(PricedOffer) {
    // Has only services not included TICKET
    const OfferItem = PricedOffer?.OfferItem || [];
    const offerItemsHasDefinition = OfferItem.filter((el) => el.Service.some((el) => has(el, 'Definition')));
    if (!offerItemsHasDefinition.length) return 0;

    return offerItemsHasDefinition.reduce((acc, item) => {
      let amount = acc;
      const TotalAmount = item?.Price?.TotalAmount || {};
      return (TotalAmount?.Amount || 0) + amount;
    }, 0);
  }

  async function reCalculatePricePrimeBooking() {
    const { OfferItems, ResponseID, OfferID, Owner, paxListPayload } = getServiceOffersRecalculate();
    const baseOffer = getBaseOffers();
    const payload = {
      Query: {
        Offers: [
          baseOffer,
          {
            OfferID,
            Owner,
            ResponseID,
            OfferItems,
          },
        ],
        PaxList: paxListPayload,
        Criteria: {
          ProgramCriteria: [],
          PromotionCriteria: [],
          SpecialFareCriteria: [],
        },
      },
    };
    if (['AF', 'KL'].includes(airlineID)) {
      payload.Query.Criteria = {
        ExistingOrderCriteria: {
          OrderID: orderId,
          PaxRefID: paxListPayload.map((el) => el.PaxID),
        },
      };
    }
    try {
      const response = await getOfferPrice(payload);
      if (response?.data?.ResultMessage?.Code === CODE_SUCCESS) {
        const data = response?.data;
        const PricedOffer = data?.PricedOffer;
        dataOfferPrice = { ...PricedOffer };

        totalAmountServices = getTotalAmountServices(PricedOffer);
        curCode = PricedOffer?.TotalPrice?.TotalAmount.CurCode || EMPTY;
      }
    } catch (error) {
      console.log(error);
    }
  }

  function handleSelectService(e, indexRow, cell) {
    const value = e.detail.selectedId;
    const selectedItem = e.detail.selectedItem;
    if (value === EMPTY) return;
    const { ptc, paxId, serviceAmountOptions } = ptcList[selectedTab][indexRow];
    const selectedService = serviceAmountOptions.find((el) => el.id === Number(value));
    const { name, amount, curCode } = selectedService;
    const findItem = selectedServices.find(
      (el) =>
        el.text === selectedService.text &&
        isEqual(el.PaxRefID, selectedService.PaxRefID) &&
        isEqual(el.PaxSegmentRefID, selectedService.PaxSegmentRefID)
    );
    if (findItem) {
      const isMultiple = PRIME_BOOKING_SERVICE_HAS_MULTIPLE.includes(name);
      findItem.count = isMultiple ? findItem.count + 1 : 0;
      findItem.warn = isMultiple ? EMPTY : '이미 선택한 서비스는 추가할 수 없습니다';

      selectedServices = [...selectedServices];
    } else {
      const item = {
        ...selectedService,
        id: uuidv4(),
        ptc,
        name,
        amount,
        paxId,
        curCode,
        tab: `구간 ${selectedTab + 1}`,
        selectedTab,
        count: 1,
        warn: EMPTY,
      };
      if (item?.isSelectOneForAll) {
        const items = ptcList[selectedTab].map((el) => {
          const foundSeatService = headerTabs[selectedTab].serviceAmountOptions[item.type]?.find(
            (service) =>
              isEqual([el.paxId], service.PaxRefID) && isEqual(selectedService.PaxSegmentRefID, service.PaxSegmentRefID)
          );
          if (!foundSeatService) return EMPTY;
          return {
            ...item,
            ...foundSeatService,
            PaxRefID: [el.paxId],
            ptc: el.ptc,
            name: selectedService.text.split('/').slice(0, -1).join(' / '),
            nameOriginal: name,
            id: item.id,
          };
        });
        selectedServices = [...compact(items), ...selectedServices];
        setStatusDisableForAllService(selectedService, true);
      } else {
        selectedServices = [item, ...selectedServices];
      }
    }
    ptcList[selectedTab][indexRow][cell.key] = EMPTY;

    if (['AF', 'KL', 'EK'].includes(airlineID)) {
      ptcList[selectedTab][indexRow].serviceAmountOptions = ptcList[selectedTab][indexRow].serviceAmountOptions.map(
        (el) => ({ ...el, disabled: true })
      );
    }

    autoCalculateServiceAmountOptions(selectedServices);
  }

  function setStatusDisableForAllService(service, disable) {
    ptcList.forEach((ptc) => {
      ptc.forEach((el) => {
        el.serviceAmountOptions = el.serviceAmountOptions.map((el) => {
          if (isEqual(el.PaxSegmentRefID, service.PaxSegmentRefID)) el.disabled = disable;
          return el;
        });
      });
    });
  }

  function deleteService(service) {
    const indexRow = ptcList[service.selectedTab].findIndex((el) => el.paxId === service.paxId);
    selectedServices = selectedServices.filter((el) => el.id !== service.id);
    if (service.Service[0].Definition.Name === SERVICE_NAME.CASH_UPGRADE) {
      setStatusDisableForAllService(service, false);
    }
    if (['AF', 'KL', 'EK'].includes(airlineID)) {
      ptcList[service.selectedTab][indexRow].serviceAmountOptions = ptcList[service.selectedTab][
        indexRow
      ].serviceAmountOptions.map((el) => ({ ...el, disabled: false }));
    }
    autoCalculateServiceAmountOptions(selectedServices);
  }

  function handleSubmitPrimeBooking() {
    let data = {};
    if (PRIME_BOOKING_SERVICE_RECALCULATE_WHITE_LIST.includes(airlineID)) {
      data = {
        Order: [
          {
            OfferID: dataOfferPrice.OfferID,
            Owner: dataOfferPrice.Owner,
            ResponseID: dataOfferPrice.ResponseID,
            OfferItems: dataOfferPrice.OfferItem.map((item) => ({
              OfferItemID: item.OfferItemID,
              PaxRefID: item.PaxRefID,
            })),
          },
        ],
        TotalPrice: dataOfferPrice.TotalPrice,
        selectedServices: dataOfferPrice.OfferItem.filter((elm) => elm.Service.some((service) => service.Definition)),
      };
    }
    if (PRIME_BOOKING_SERVICE_NOT_RECALCULATE_WHITE_LIST.includes(airlineID)) {
      const services = serviceList?.data?.AlaCarteOffer?.AlaCarteOfferItem?.filter((elm) =>
        selectedServices.some((service) => service.OfferItemID === elm.OfferItemID)
      );
      data = {
        Order: [
          {
            OfferID: serviceList?.data?.AlaCarteOffer?.OfferID,
            Owner: serviceList?.data?.AlaCarteOffer?.Owner,
            OfferItems: services.map((elm) => {
              return {
                OfferItemID: elm.OfferItemID,
                PaxRefID: elm.PaxRefID,
              };
            }),
          },
        ],
        selectedServices: services,
        TotalPrice: {
          TotalAmount: {
            Amount: totalAmountServices,
            CurCode: curCode,
          },
        },
      };
    }
    dispatch('submit-selected-services', data);
    handleCloseModal();
  }

  async function handleBuyServicePrimeBooking() {
    // if ()
  }

  function getServiceAmountOptions(segments) {
    const BASE_SERVICE_MAP = {
      [PRIME_BOOKING_SERVICE_TYPE.BAGGAGE]: [],
      [PRIME_BOOKING_SERVICE_TYPE.SEAT]: [],
      [PRIME_BOOKING_SERVICE_TYPE.MEAL]: [],
      [PRIME_BOOKING_SERVICE_TYPE.ETC]: [],
    };

    const { AlaCarteOfferItem = [] } = serviceList?.data?.AlaCarteOffer;

    const filteredBySegment = AlaCarteOfferItem.filter((offerItem) => {
      if (
        difference(offerItem.PaxSegmentRefID, segments).length === 0 &&
        difference(segments, offerItem.PaxSegmentRefID).length === 0
      ) {
        return true;
      }
    });

    function filterServiceByName(dataList, serviceList, isIncludes = false) {
      return dataList.filter((el) =>
        isIncludes
          ? el.Service[0].Definition.Name.includes(serviceList)
          : serviceList.includes(el.Service[0].Definition.Name)
      );
    }

    if (['AY'].includes(airlineID)) {
      BASE_SERVICE_MAP.baggage = filterServiceByName(filteredBySegment, PRIME_BOOKING_SERVICE.GROUP_AY.BAGGAGE);
    } else if (['TR', 'HA', 'HAA'].includes(airlineID)) {
      BASE_SERVICE_MAP.baggage = filteredBySegment; // get all service
    } else if (['SQ', 'QR'].includes(airlineID)) {
      const acceptedListService = PRIME_BOOKING_SERVICE.GROUP_SQ_QR.BAGGAGE;
      BASE_SERVICE_MAP.baggage = filterServiceByName(filteredBySegment, acceptedListService);
    } else if (['AF', 'KL'].includes(airlineID)) {
      BASE_SERVICE_MAP.baggage = filterServiceByName(
        filteredBySegment,
        PRIME_BOOKING_SERVICE.GROUP_AF_KL.BAGGAGE,
        true
      );
    }

    Object.entries(BASE_SERVICE_MAP).forEach(([serviceKey, items]) => {
      BASE_SERVICE_MAP[serviceKey] = transformServiceAmountOptions(
        items.map((item) => ({
          ...item,
          type: serviceKey,
          isSelectOneForAll: false,
        }))
      );
    });

    const totalService = Object.values(BASE_SERVICE_MAP).reduce((acc, val) => acc + val.length, 0);

    return {
      result: BASE_SERVICE_MAP,
      totalService,
    };
  }

  function autoCalculateServiceAmountOptions(selectedServices) {
    totalAmountServices = selectedServices.reduce((acc, obj) => acc + obj.amount * obj.count || 1, 0);
    curCode = selectedServices[0]?.curCode || EMPTY;
  }

  function createServiceAmountOptions(isHiddenServiceType, row) {
    if (!isHiddenServiceType) return [];
    const { paxId } = row;

    const tab = headerTabs[selectedTab];
    const serviceAmountOptionsOfTab = Object.values(tab.serviceAmountOptions).flat();
    const filteredServiceByPaxID = serviceAmountOptionsOfTab.filter((el) => el.PaxRefID.includes(paxId));

    return filteredServiceByPaxID;
  }

  function createServiceTypeOptions(tabId, row) {
    const tab = headerTabs[tabId];
    const paxId = row.paxId;

    const serviceTypes = [
      { key: PRIME_BOOKING_SERVICE_TYPE.BAGGAGE, id: '0', text: '위탁수하물' },
      { key: PRIME_BOOKING_SERVICE_TYPE.SEAT, id: '1', text: '좌석승급' },
      { key: PRIME_BOOKING_SERVICE_TYPE.MEAL, id: '2', text: '기내식' },
      { key: PRIME_BOOKING_SERVICE_TYPE.ETC, id: '3', text: '기타서비스' },
    ];

    return serviceTypes
      .filter(({ key }) => {
        const services = tab.serviceAmountOptions[key];
        return Array.isArray(services) && services.some((el) => el.PaxRefID.includes(paxId));
      })
      .map(({ id, text, key }) => ({ id, text, value: key }));
  }

  function handleChangeTab(tabId) {
    if (tabId === selectedTab) return;
    selectedTab = tabId;
    generatePtcList(tabId);
  }

  function generatePtcList(tabId) {
    // HA (HAA) and TR -> hidden serviceType
    if (['TR', 'HA', 'HAA'].includes(airlineID)) {
      isHiddenServiceType = true;
      baggageTableHeaders = baggageTableHeaders.filter((el) => el.key !== 'serviceType');
    }

    const { PaxJourneyList = [], PaxList = [] } = orderRetrieve?.data?.DataLists;
    ptcList = PaxJourneyList.map((_) => {
      const paxListNotIncloudedInf = sortPaxListByPtc(PaxList).filter((el) => el.Ptc !== 'INF');
      return paxListNotIncloudedInf.map((pax, i) => {
        return {
          id: i,
          paxId: pax.PaxID,
          ptc: `탑승객 ${i + 1} (${pax.Ptc})`,
          serviceType: EMPTY,
          serviceAmount: EMPTY,
          serviceAmountOptions: createServiceAmountOptions(isHiddenServiceType, { paxId: pax.PaxID }),
          serviceTypeOptions: createServiceTypeOptions(tabId, { paxId: pax.PaxID }),
        };
      });
    });
  }

  onMount(() => {
    if (serviceList?.data && orderRetrieve?.data?.ResultMessage?.Code === CODE_SUCCESS) {
      const { PaxJourneyList = [] } = orderRetrieve?.data?.DataLists;
      headerTabs = PaxJourneyList.map(({ OnPoint, OffPoint, PaxSegmentRefID = [] }, i) => ({
        id: i,
        label: `구간${i + 1}: ${OnPoint}-${OffPoint}`,
        segments: PaxSegmentRefID,
        serviceAmountOptions: getServiceAmountOptions(PaxSegmentRefID).result,
        totalServiceAmount: getServiceAmountOptions(PaxSegmentRefID).totalService,
      }));

      generatePtcList(headerTabs[0].id);
    }
  });
</script>

<Portal {idBox}>
  <div id="baggage-information">
    <ComposedModal size="lg" {open} preventCloseOnClickOutside={true} on:close={handleCloseModal}>
      <ModalHeader title="서비스" />
      <ModalBody>
        <div class="baggage-container">
          <Tabs type="container">
            {#each headerTabs as tab}
              <Tab label={tab?.label} on:click={() => handleChangeTab(tab.id)} />
            {/each}
          </Tabs>
          <div class="baggage-wrapper">
            <div class="baggage-container--left">
              {#if headerTabs[selectedTab]?.totalServiceAmount > 0}
                <div class="baggage-table">
                  {#key selectedTab}
                    <DataTable headers={baggageTableHeaders} rows={ptcList[selectedTab]}>
                      <div slot="cell" let:cell let:row>
                        {@const indexRow = ptcList[selectedTab].findIndex((el) => el.id === row.id)}
                        {#if cell.key === 'serviceType'}
                          <ComboBox
                            size="sm"
                            placeholder="선택"
                            on:clear={() => clearServiceType(indexRow)}
                            bind:selectedId={ptcList[selectedTab][indexRow][cell.key]}
                            on:select={(e) => changeServiceType(e, indexRow, cell)}
                            items={ptcList[selectedTab][indexRow].serviceTypeOptions}
                          />
                        {:else if cell.key === 'serviceAmount'}
                          <ComboBox
                            size="sm"
                            placeholder="선택"
                            bind:selectedId={ptcList[selectedTab][indexRow][cell.key]}
                            items={ptcList[selectedTab][indexRow].serviceAmountOptions}
                            disabled={isEmpty(ptcList[selectedTab][indexRow].serviceAmountOptions)}
                            on:select={(e) => handleSelectService(e, indexRow, cell)}
                          />
                        {:else}
                          {cell.value}
                        {/if}
                      </div>
                    </DataTable>
                  {/key}
                </div>
              {:else}
                <div class="baggage-container-empty">해당 구간은 서비스 구매가 불가합니다</div>
              {/if}
            </div>
            <div class="baggage-container--right">
              <div>
                <div class="baggage--right-head">
                  <div class="text-align-l">선택 구간</div>
                  <div class="col-span-3 text-align-l">탑승객명 (PTC)</div>
                  <div class="col-span-3 text-align-l">서비스</div>
                  <div class="col-span-2">금액</div>
                  <div class="col-span-2 text-align-r" />
                  <div />
                </div>
                <div class="baggage--right-middle">
                  {#if !selectedServices.length}
                    <div
                      style="text-align: center;align-items: center;justify-content: center;height: 50px;display: flex;"
                    >
                      선택한 서비스가 없습니다
                    </div>
                  {/if}
                  {#each selectedServices as service, i (i)}
                    {@const serviceName = service.name}
                    <div class="right-middle-item" class:selected={service.selected} role="button" tabindex="0">
                      <div class="text-align-l">
                        {service.tab}
                      </div>
                      <div class="col-span-3 text-align-l">{service.ptc}</div>
                      <div class="col-span-3 text-align-l">
                        {#if ['EK'].includes(airlineID)}
                          {service.name} / {service.Service[0].Definition.Desc[0].Text}
                        {:else}
                          {service.name}
                        {/if}
                      </div>
                      <div class="col-span-2 text-align-r">
                        {service.amount ? `${formatPriceNumber(service.amount)} ${service.curCode}` : EMPTY}
                      </div>
                      <div class="col-span-2 text-align-r">
                        {#if PRIME_BOOKING_SERVICE_HAS_MULTIPLE.includes(serviceName)}
                          <div
                            class="g-btn-action"
                            style="display: flex; gap: 10px; align-items: center;justify-content: end"
                          >
                            <button
                              style="all: unset"
                              on:click={() => {
                                service.count > 1 ? (service.count = service.count - 1) : (service.count = 1);
                                autoCalculateServiceAmountOptions(selectedServices);
                              }}
                            >
                              <SubtractAlt size={18} />
                            </button>
                            <p style="padding-right: 0">{service.count}</p>
                            <button
                              style="all: unset"
                              on:click={() => {
                                service.count = service.count + 1;
                                autoCalculateServiceAmountOptions(selectedServices);
                              }}
                            >
                              <AddAlt size={18} />
                            </button>
                          </div>
                        {/if}
                      </div>
                      <div class="btn-delete">
                        {#if service?.amount}
                          <button style="all: unset" on:click={() => deleteService(service)}>
                            <TrashCan size="14" />
                          </button>
                        {/if}
                      </div>
                    </div>
                    {#if service.warn.length}
                      {@const deleteTimeout = setTimeout(() => {
                        service.warn = '';
                      }, 3000)}
                      <div style="color: red">{service.warn}</div>
                    {/if}
                  {/each}
                </div>
              </div>
              <div class="baggage--right-bottom">
                <div>총 금액</div>
                <div>{formatPriceNumber(totalAmountServices)} {curCode}</div>
              </div>
            </div>
          </div>
        </div>
      </ModalBody>
      <ModalFooter>
        <footer>
          <PrimaryButton data-modal-primary-focus kind="secondary" width="100%" on:click={handleCloseModal}
            >취소</PrimaryButton
          >
          <PrimaryButton width="100%" on:click={() => dialog.showModal()}>
            서비스선택
          </PrimaryButton>
        </footer>
      </ModalFooter>
    </ComposedModal>
  </div>
</Portal>

<AddOnServiceConfirmRequest
  bind:dialog
  on:confirm-add-service={async () => {
    handleBuyServicePrimeBooking();
  }}
/>

<style>
  .baggage-container-empty {
    min-height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #cccccc;
    border-radius: 4px;
  }
</style>
