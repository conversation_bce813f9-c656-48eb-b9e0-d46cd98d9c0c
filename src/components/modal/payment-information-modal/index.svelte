<script>
  import { AmountInformationTable } from 'src/components/common/table';
  import { getOffer } from 'src/service/reservationTicketing.js';
  import { has, cloneDeep } from 'lodash';
  import { ComposedModal, ModalHeader, ModalBody, ModalFooter } from 'carbon-components-svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import { InlineLoading } from 'carbon-components-svelte';
  import PassengerInformation from './passenger-information/index.svelte';
  import { EMPTY, CODE_SUCCESS } from 'src/constants/app';
  import { getMainBookingReference } from 'src/utils/appHelper';
  import PaymentInformationTable from './PaymentInformationTable.svelte';
  import { bookingReference, screenIsNotEvent, message } from 'src/store';
  import Portal from 'src/components/common/Portal.svelte';
  import ItineraryInformationTable from './ItineraryInformationTable.svelte';
  import 'src/styles/modal/payment-information-modal.scss';
  import { PRIME_BOOKING_SERVICE_NOT_RECALCULATE_WHITE_LIST, PRIME_BOOKING_SERVICE_RECALCULATE_WHITE_LIST } from 'src/constants/baggageService';

  export let open = false;
  export let DataLists = {};
  export let PricedOffer = {};

  const airlineID = PricedOffer && has(PricedOffer, 'Owner') ? PricedOffer.Owner : EMPTY;

  let isLoadingOffer = false;
  let paramsOfferQuery = {};
  let refPassengerInformation;
  let refAmountInformationTable;
  let dataBuyService = EMPTY;
  let checkedPaymentType = EMPTY;

  const handleActionPaymentOrIssuance = async () => {
    if (refPassengerInformation?.checkInValid()) return;

    if (!paramsOfferQuery?.Order) {
      paramsOfferQuery.Order = getDefaultOrder(PricedOffer);
    }
    try {
      isLoadingOffer = true;
      screenIsNotEvent.set(true);
      const response = await getOffer({ Query: paramsOfferQuery });
      isLoadingOffer = false;
      screenIsNotEvent.set(false);
      const { code, data } = response;
      if (code === 200 && data?.ResultMessage?.Code === CODE_SUCCESS) {
        message.set([
          {
            type: 'success',
            title: 'Success',
            subtitle: paramsOfferQuery.PaymentList.length === 0 ? '예약이 완료되었습니다' : '발권이 되었습니다',
          },
        ]);
        let findB = getMainBookingReference(data?.Order?.BookingReference);
        bookingReference.set({
          booking_reference: findB?.Id ?? EMPTY,
          order_id: data?.Order?.OrderID,
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      open = false;
      return;
    }
  };

  const handleUpdateParamsOfferQuery = (event) => {
    paramsOfferQuery = { ...paramsOfferQuery, ...event.detail };
  };

  const getDefaultOrder = (PricedOffer) => {
    const { OfferID, Owner, ResponseID, OfferItem } = PricedOffer;
    const Order = [
      {
        OfferID,
        Owner,
        ResponseID,
        OfferItems: OfferItem.map((item) => ({
          OfferItemID: item.OfferItemID,
          Owner: item.Owner,
          PaxRefID: item.PaxRefID,
        })),
      },
    ];
    return Order;
  };

  const handleNewService = (event) => {
    if (PRIME_BOOKING_SERVICE_RECALCULATE_WHITE_LIST.includes(airlineID)) {
      paramsOfferQuery = { ...paramsOfferQuery, Order: event.detail.Order };
    }
    if (PRIME_BOOKING_SERVICE_NOT_RECALCULATE_WHITE_LIST.includes(airlineID)) {
      const Order = getDefaultOrder(PricedOffer);
      paramsOfferQuery = { ...paramsOfferQuery, Order: [...Order, ...event.detail.Order] };
    }
    dataBuyService = event.detail;
  };

  function mappingPaxRefIdForInf(paxList) {
    const clonedPaxList = cloneDeep(paxList);
    if (!clonedPaxList?.length) return [];

    const adults = clonedPaxList.filter((p) => p.Ptc === 'ADT');
    let adultIndex = 0;

    return clonedPaxList.map((p) => {
      if (p.Ptc === 'INF') {
        const assignedAdult = adults[adultIndex];
        adultIndex++;
        return {
          ...p,
          PaxRefID: assignedAdult.PaxID,
        };
      }
      return p;
    });
  }

  async function handlePurchaseService() {
    if (refAmountInformationTable) {
      await refAmountInformationTable.openModalBuyService();
    }
  }
</script>

<Portal>
  <div id="PassengerInformationModal">
    <ComposedModal bind:open size="lg" preventCloseOnClickOutside={true}>
      <ModalHeader>
        <h4>결제 정보</h4>
      </ModalHeader>
      <ModalBody>
        <div class="wrapper-layout-content">
          <ItineraryInformationTable {DataLists} {PricedOffer} isPrimeBooking={true} {airlineID} handlePurchaseService={handlePurchaseService} />
        </div>
        <div class="wrapper-layout-content">
          <PassengerInformation
            PaxList={mappingPaxRefIdForInf(DataLists.PaxList)}
            on:change={handleUpdateParamsOfferQuery}
            bind:this={refPassengerInformation}
            arline={PricedOffer?.Owner}
          />
        </div>
        <div class="wrapper-layout-content">
          <AmountInformationTable
            {DataLists}
            {PricedOffer}
            {airlineID}
            isPrimeBooking
            bind:this={refAmountInformationTable}
            on:update-new-service={handleNewService}
          />
        </div>
        <div class="wrapper-layout-content">
          <PaymentInformationTable
            bind:checkedValue={checkedPaymentType}
            pricedOffer={PricedOffer}
            TotalPrice={dataBuyService.TotalPrice}
            airlineId={airlineID}
            on:change={handleUpdateParamsOfferQuery}
          />
        </div>
      </ModalBody>
      <ModalFooter>
        <footer>
          <div class="action-button">
            <PrimaryButton width="100%" disabled={!checkedPaymentType} on:click={handleActionPaymentOrIssuance}
              >결제&발행

              {#if isLoadingOffer}
                <div class="g-custom-loading-button">
                  <InlineLoading description="Loading ..." />
                </div>
              {/if}
            </PrimaryButton>
          </div>
          <div class="action-button">
            <PrimaryButton kind="secondary" on:click={() => (open = false)} width="100%">닫기</PrimaryButton>
          </div>
        </footer>
      </ModalFooter>
    </ComposedModal>
  </div>
</Portal>
