.changeOfItinerary {
  padding: 0 !important;
  margin: 0 !important;
  .bx--modal-container {
    max-width: 1600px !important;
    width: 100%;
    max-height: unset;
    .bx--modal-content {
      margin-bottom: 0;
      padding: 0;
    }
    .bx--modal-content.step_two {
      height: 75vh;
    }
    @include maxScreen {
      .bx--modal-content.step_three {
        max-height: calc(70vh);
        height: auto;
      }
    }
  }

  header {
    margin-top: 16px;
    padding: 8px 16px 20px 16px;
    .step-progress {
      .bx--progress-label {
        width: 80px;
      }
      .bx--progress-optional {
        margin-top: 28px;
      }
    }
  }
  footer {
    width: 100%;
    button {
      padding: 0 !important;
      height: 40px !important;
      min-height: unset !important;
    }
    .step-one {
      width: 100%;
      display: flex;
      justify-content: end;
      align-items: center;
      button {
        flex: 0 0 33.33%;
        max-width: unset !important;
        padding-left: 24px !important ;
        padding-right: 0 !important ;
      }
    }
    .step-two,
    .step-three {
      width: 100%;
      display: flex;
      justify-content: end;
      align-items: center;
      button {
        flex: 0 0 25%;
        max-width: unset !important;
        padding-left: 16px !important;
        padding-right: 0 !important ;
      }
      .loading-steptwo .bx--inline-loading {
        .bx--inline-loading__text {
          color: white !important;
          font-size: 16px;
          padding-right: 32px;
        }
      }
    }
  }
  .container {
    padding: 0;
    height: auto;
  }

  .chooseAChangeItinerary {
    padding: 24px 16px 48px;
    h4 {
      font-size: 16px;
      margin: 0 0 8px 16px;
    }
    ul li {
      padding: 4px 12px 4px 16px;
    }
    ul {
      padding: 0;
    }
    td {
      padding: 0;
    }
    .delimiter {
      height: 1px;
      background-color: #e0e0e0;
    }
    .space-incognito {
      visibility: collapse;
    }
    .bx--form-item.bx--checkbox-wrapper {
      align-items: flex-end;
    }
    .type-journey {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
      h4 {
        margin: 0 0 8px 16px;
        min-width: 80px;
        font-size: 16px;
      }
      .bx--dropdown {
        min-width: 180px;
        max-width: 200px;
        width: 140px;
      }
    }
  }
  .checkAndChangeItinerary {
    padding: 0 16px;
    max-height: calc(100vh - 400px);
    .wrapper-content {
      .show-tooltip {
        display: flex;
        align-items: center;
        padding-bottom: 16px;
        h4 {
          padding-bottom: 0;
          padding-right: 8px;
        }

        .bx--assistive-text {
          max-width: 500px;
          padding: 8px;
        }
        li + li {
          margin-top: 4px;
        }
      }
      margin-top: 24px;
      h4 {
        margin-left: 16px;
        padding-bottom: 16px;
      }
    }
    .wrapper-content .payment-method {
      width: 180px;
    }
  }

  .bx--list-box--disabled .bx--list-box__selection > svg {
    display: none;
  }
}
