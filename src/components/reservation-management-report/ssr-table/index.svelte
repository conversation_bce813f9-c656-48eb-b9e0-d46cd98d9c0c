<script>
  import { DataTable, Toolbar, InlineLoading, Checkbox } from 'carbon-components-svelte';
  import { isEqual } from 'lodash';
  import { onMount } from 'svelte';
  import { isLocaleLoaded, _ } from 'src/lib/i18n.js';
  import { getAmountAndOrderItemIDList, getSsrData, groupData } from 'src/utils/appHelper.js';
  import { EMPTY } from 'src/constants/app.js';
  import { PrimaryButton } from 'src/components/common/button';
  import { screenIsNotEvent, user } from 'src/store';
  import Portal from 'src/components/common/Portal.svelte';
  import ChangePaymentPnr from 'src/components/modal/ChangePaymentPnr.svelte';
  import { PERMISSIONS, ROLES } from 'src/utils/rolePermissions';
  import CancelSsrModal from 'src/components/modal/CancelSsrModal.svelte';
  import PermissionGuard from 'src/components/common/PermissionGuard.svelte';

  export let resOrderRetrieve;
  export let airlineID;
  export let isPayLater;
  let headers = [
    {
      key: 'airlineID',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.airlineID', {
        default: '항공사',
      }),
      width: '12%',
    },
    {
      key: 'givenNameAndPtc',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.givenNameAndPtc', {
        default: '탑승객명(PTC)',
      }),
      width: '20%',
    },
    {
      key: 'name',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.name', {
        default: 'SSR',
      }),
      width: '20%',
    },
    {
      key: 'status',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.status', {
        default: '상태',
      }),
      width: '8%',
    },
    {
      key: 'segments',
      value: $_('layout.pages.reservationManagement.ssrTable.dataTable.headers.flightNumberAirportCodeDate', {
        default: 'Segments',
      }),
      width: '20%',
    },
  ];

  const airLineCase1 = ['LH', 'LHA', 'LX', 'LXA', 'OS', 'EK'];
  const airlineCase2 = ['KE', 'HA', 'HAA'];
  const isUser = $user.role === ROLES.USER;
  let ssrTable = [];
  let hasStatusWarn = false;
  let isLoading = false;
  let changePaymentPnrData = {
    open: false,
    payload: {},
  };

  let totalAmount = {
    Amount: 0,
    CurCode: EMPTY,
  };
  let openCancelSsrModal = false;

  let isCancelSSR = false;

  $: if (!isUser) {
    headers = [
      ...headers,
      {
        key: 'checked',
        value: EMPTY,
        width: '5%',
      },
    ];
  }

  const sortData = (dataTable) => {
    return dataTable.sort((a, b) => {
      if (a['Ptc'] === b['Ptc']) {
        return a['PaxID'].localeCompare(b['PaxID']);
      }
      return a['Ptc'].localeCompare(b['Ptc']);
    });
  };

  const handleOrderChange = async (dataTable) => {
    const {
      OrderItemIDList = [],
      Amount = 0,
      CurCode = EMPTY,
    } = getAmountAndOrderItemIDList(dataTable, resOrderRetrieve?.data?.DataLists?.PaymentList);

    try {
      isLoading = true;
      const OrderID = resOrderRetrieve?.data?.Order?.OrderID || EMPTY;

      const payload = {
        query: {
          OrderID,
          PaxList: [],
          ContactInfoList: [],
          ChangeOrderChoice: {
            AcceptRepricedOrder: {
              OfferRefID: [OrderID],
            },
            UpdatePax: [],
          },
          PaymentList: [
            {
              Type: 'Cash',
              Amount,
              CurCode,
              PaxRefID: [],
              OrderItemID: OrderItemIDList,
              OfferItemID: [],
            },
          ],
        },
      };
      changePaymentPnrData = {
        open: true,
        payload,
      };
      totalAmount = {
        Amount,
        CurCode,
      };
    } catch (error) {
      console.log(error);
    } finally {
      isLoading = false;
    }
  };

  const checkDisabledPurchase = (data) => {
    if ([...airLineCase1, ...airlineCase2].includes(airlineID)) {
      const checkHasChecked = data.find((row) => row.checked);
      if (checkHasChecked) return false;
    }
    return true;
  };

  const getOrderItemWithConditionAirlineCase2 = () => {
    let orderItems = [];
    orderItems = resOrderRetrieve?.data?.Order?.OrderItem?.filter((item) =>
      item?.Service?.some((service) => ['HD', 'CONFIRMED'].includes(service.Status))
    );

    orderItems = orderItems?.filter((item) => {
      return item.FareDetail.some((fare) => fare?.BaseAmount?.Amount !== 0);
    });

    const orderItemIDInPaymentList = resOrderRetrieve?.data?.DataLists?.PaymentList.reduce((acc, item) => {
      return acc.concat(item.OrderItemID);
    }, []);
    orderItems = orderItems?.filter((orderItem) => {
      return !orderItemIDInPaymentList.includes(orderItem.OrderItemID);
    });

    let serviceIds = [];
    for (const orderItem of orderItems) {
      serviceIds = serviceIds.concat(
        orderItem.Service.filter((service) => ['HD', 'CONFIRMED'].includes(service.Status)).map(
          (service) => service.ServiceID
        )
      );
    }

    const ticketDocList = resOrderRetrieve?.data?.DataLists?.TicketDocList || [];
    let allCouponInfos = [];
    ticketDocList.forEach((ticketDoc) => {
      (ticketDoc.TicketDocument || []).forEach((doc) => {
        if (doc?.CouponInfos?.length) {
          allCouponInfos = allCouponInfos.concat(doc.CouponInfos);
        }
      });
    });
    const serviceIdInCouponInfo = allCouponInfos.reduce((acc, coupon) => {
      return acc.concat(coupon.ServiceRefID);
    }, []);
    if (!serviceIdInCouponInfo.length) return orderItems;
    const intersection = serviceIdInCouponInfo.filter((item) => serviceIds.includes(item));
    if (intersection.length) return [];
    return orderItems;
  };

  const checkHiddenButton = (data) => {
    if (airLineCase1.includes(airlineID)) {
      const checkHasStatusHD = data.find((row) => row.status === 'HD');
      if (checkHasStatusHD) return false;
    }
    if (airlineCase2.includes(airlineID)) {
      const orderItems = getOrderItemWithConditionAirlineCase2();

      if (orderItems?.length) return false;
    }
    return true;
  };

  const handleSelectedPurchase = (event, currentRow) => {
    ssrTable = ssrTable.map((row) => {
      if (isEqual(row, currentRow)) row.checked = event.detail;
      return row;
    });
  };
  const handleSelectedAllPurchase = (event) => {
    const isUnCheck =
      ssrTable.filter((item) => item.status === 'HD').length ===
      ssrTable.filter((item) => item.status === 'HD' && item.checked).length;

    ssrTable = ssrTable.map((row) => {
      if (event.detail) row.checked = event.detail;
      else {
        if (isUnCheck) row.checked = event.detail;
      }
      return row;
    });
  };
  onMount(() => {
    ssrTable = groupData(getSsrData(resOrderRetrieve, airlineID));
    for (const ssrItem of ssrTable) {
      isCancelSSR =
        ['KE', 'HA', 'HAA'].includes(airlineID) &&
        ssrItem.status !== 'CANCELLED' &&
        ssrItem.FareDetail.some((fare) => fare?.BaseAmount?.Amount === 0) &&
        !resOrderRetrieve?.data?.DataLists?.PaymentList.find((elm) => elm.OrderItemID === ssrItem.OrderItemID);
      if (isCancelSSR) {
        break;
      }
    }
    hasStatusWarn = ssrTable.some((item) => ['HD', 'HN'].includes(item?.Status));
  });
  $: screenIsNotEvent.set(isLoading);
</script>

{#if $isLocaleLoaded}
  <DataTable id="table-management" {headers} rows={sortData(ssrTable)}>
    <Toolbar size="sm" class="table-toolbar">
      <div class="bx--toolbar-content">
        <div class="title-table">
          <h4 class="title-table-content">
            {$_('layout.pages.reservationManagement.ssrTable.toolbar.titleTable', {
              default: 'SSR 정보',
            })}
          </h4>
        </div>

        <div class="g-wrapper-toolbar-button">
          {#if hasStatusWarn}
            <div style="color: red; padding-left: 0.5rem">구매 대기 중인 부가서비스가 있습니다.</div>
          {/if}
          <div class="button-action">
            {#if [...airLineCase1, ...airlineCase2].includes(airlineID) && !isPayLater && !checkHiddenButton(ssrTable) && !isUser}
              <PrimaryButton
                kind="secondary"
                size="small"
                on:click={() => handleOrderChange(ssrTable)}
                height="32"
                width="180"
                disabled={checkDisabledPurchase(ssrTable)}
              >
                {#if isLoading}
                  <div class="g-custom-loading-button loading-box">
                    <InlineLoading description="Loading" />
                  </div>
                {:else}
                  구매확정
                {/if}
              </PrimaryButton>
            {/if}

            <PermissionGuard permission={PERMISSIONS.CANCEL_SSR}>
              {#if isCancelSSR}
                <PrimaryButton
                  kind="danger-tertiary"
                  size="small"
                  height="32"
                  width="180"
                  on:click={() => (openCancelSsrModal = true)}>SSR 취소</PrimaryButton
                >
              {/if}
            </PermissionGuard>
          </div>
        </div>
      </div>
    </Toolbar>
    <div slot="cell-header" let:header>
      {#if header.key === 'checked'}
        {#if [...airLineCase1, ...airlineCase2].includes(airlineID) && !checkHiddenButton(ssrTable) && !isPayLater}
          {@const isAllChecked = !ssrTable.find(
            (item) => ['HD', 'CONFIRMED'].includes(item.status) && item.checked === false
          )}
          <Checkbox hideLabel on:check={handleSelectedAllPurchase} checked={isAllChecked} />
        {/if}
      {:else}
        {header.value}
      {/if}
    </div>
    <div slot="cell" let:cell let:row>
      {#if ['segments'].includes(cell.key)}
        <ul>
          {#each cell.value as valueItem}
            <li>
              {valueItem}
            </li>
          {/each}
        </ul>
      {:else if cell.key === 'status'}
        {cell.value}
      {:else if cell.key === 'checked'}
        {#if !isPayLater}
          {#if airLineCase1.includes(airlineID)}
            {@const showCheckbox = ['HD'].includes(row.status)}
            {#if showCheckbox}
              <Checkbox hideLabel checked={row.checked} on:check={(event) => handleSelectedPurchase(event, row)} />
            {/if}
          {/if}
          {#if airlineCase2.includes(airlineID)}
            {@const showCheckbox =
              ['HD', 'CONFIRMED'].includes(row.status) &&
              getOrderItemWithConditionAirlineCase2().length &&
              getOrderItemWithConditionAirlineCase2().some((item) => item.OrderItemID === row.OrderItemID)}
            {#if showCheckbox}
              <Checkbox hideLabel checked={row.checked} on:check={(event) => handleSelectedPurchase(event, row)} />
            {/if}
          {/if}
        {/if}
      {:else}
        {cell.value}
      {/if}
    </div>
  </DataTable>
  <Portal>
    {#if changePaymentPnrData.open}
      <ChangePaymentPnr
        orderId={resOrderRetrieve?.data?.Order?.OrderID}
        bind:open={changePaymentPnrData.open}
        airline={airlineID}
        payload={changePaymentPnrData.payload}
        {totalAmount}
      />
    {/if}
  </Portal>

  <Portal>
    <CancelSsrModal
      bind:open={openCancelSsrModal}
      dataSsrCancel={ssrTable.filter(
        (item) =>
          ['KE', 'HA', 'HAA'].includes(item.airlineID) &&
          item.status !== 'CANCELLED' &&
          item.FareDetail.some((fare) => fare?.BaseAmount?.Amount === 0) &&
          !resOrderRetrieve?.data?.DataLists?.PaymentList.find((elm) => elm.OrderItemID === item.OrderItemID)
      )}
    />
  </Portal>
{:else}
  <p>Loading...</p>
{/if}

<style>
  .g-wrapper-toolbar-button {
    align-items: center;
  }
  .button-action {
    flex-grow: 1;
    display: flex;
    flex-direction: row-reverse;
  }
</style>
