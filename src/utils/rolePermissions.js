export const ROLES = {
  ADMIN: 'luna:admin',
  TICKETING_USER: 'luna:ticketingUser',
  USER: 'luna:user',
};

export const PERMISSIONS = {
  // user
  CREATE_USER: 'create_user',
  PURCHASE_SEAT_SERVICE: 'purchase_seat_service',
  PURCHASE_SERVICE: 'purchase_service',
  SEARCH_CHANGE_ITINERARY: 'search_change_itinerary',
  CHANGE_USER_INFORMATION: 'change_user_information',
  SEPARATION_PNR: 'separation_pnr',
  VIEW_USER_LIST: 'view_user_list',
  DELETE_USER: 'delete_user',

  // payment
  INSTANT_PAYMENT: 'instant_payment',
  PAY_LATER: 'pay_later',

  VOID_REFUND_TICKET: 'void_refund_ticket',
  CANCEL_TICKET: 'cancel_ticket',
  PAYMENT_ISSUANCE: 'payment_issuance',

  // ticket
  CANCEL_TICKET: 'cancel_ticket',

  // ssr
  CANCEL_SSR: 'cancel_ssr',
};

export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [...Object.values(PERMISSIONS)],

  [ROLES.TICKETING_USER]: [
    PERMISSIONS.PURCHASE_SEAT_SERVICE,
    PERMISSIONS.PURCHASE_SERVICE,
    PERMISSIONS.SEARCH_CHANGE_ITINERARY,
    PERMISSIONS.CHANGE_USER_INFORMATION,
    PERMISSIONS.SEPARATION_PNR,
    PERMISSIONS.INSTANT_PAYMENT,
    PERMISSIONS.PAY_LATER,
    PERMISSIONS.VOID_REFUND_TICKET,
    PERMISSIONS.CANCEL_TICKET,
    PERMISSIONS.PAYMENT_ISSUANCE,
    PERMISSIONS.CANCEL_TICKET,
    PERMISSIONS.CANCEL_SSR,
  ],

  [ROLES.USER]: [PERMISSIONS.PAY_LATER],
};

export function hasPermission(userRole, permission) {
  if (!userRole || !permission) return false;

  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  return rolePermissions.includes(permission);
}

export function hasAnyPermission(userRole, permissions) {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false;

  return permissions.some((permission) => hasPermission(userRole, permission));
}

export function hasAllPermissions(userRole, permissions) {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false;

  return permissions.every((permission) => hasPermission(userRole, permission));
}

export function getRolePermissions(userRole) {
  return ROLE_PERMISSIONS[userRole] || [];
}

export function isAdmin(userRole) {
  return userRole === ROLES.ADMIN;
}

export function isTicketingUser(userRole) {
  return userRole === ROLES.TICKETING_USER;
}

export function isRegularUser(userRole) {
  return userRole === ROLES.USER;
}
