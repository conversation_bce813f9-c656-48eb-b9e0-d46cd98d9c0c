import { EMPTY } from 'src/constants/app.js';

export const USER_LIST_PAGE_SIZES = [15, 30, 50, 100];

export const USER_LIST_HEADERS = [
  {
    key: 'id',
    value: 'No.',
  },
  {
    key: 'agencyId',
    value: 'AgencyID',
  },
  {
    key: 'email',
    value: 'Email (ID)',
  },
  {
    key: 'phoneNumber',
    value: 'PhoneNumber',
  },
  {
    key: 'userType',
    value: 'UserType',
  },
  {
    key: 'status',
    value: 'Status',
  },
  {
    key: 'createdAt',
    value: 'Created Date',
  },
  {
    key: 'updatedAt',
    value: 'Last Accessed Date',
  },
];

export const USER_ROLES = [
  {
    id: 'luna:admin',
    text: 'Admin',
  },
  {
    id: 'luna:ticketingUser',
    text: 'Ticketing User',
  },
  {
    id: 'luna:user',
    text: 'User',
  },
];

export const USER_STATUSES = [
  {
    id: 'Inactive',
    text: 'Invited',
  },
  {
    id: 'Active',
    text: 'Active',
  },
];

export const USER_LIST_FORM_OPTION = {
  createdFrom: EMPTY,
  createdTo: EMPTY,
  roles: [],
  statuses: [],
  email: EMPTY,
};

export const USER_TYPE_MAP = {
  'luna:user': 'User',
  'luna:admin': 'Admin',
  'luna:ticketingUser': 'Ticketing',
};

export const USER_TYPE_DESCRIPTION = {
  [USER_TYPE_MAP['luna:admin']]: {
    name: 'Admin',
    desc: 'LUNA의 모든 메뉴(예약·발권·유저 관리)를 사용할 수 있습니다.',
  },
  [USER_TYPE_MAP['luna:ticketingUser']]: {
    name: 'Ticketing User',
    desc: '유저 관리 메뉴를 제외하고 LUNA의 모든 예약·발권 기능을 사용할 수 있습니다.',
  },
  [USER_TYPE_MAP['luna:user']]: {
    name: 'User',
    desc: '발권·예약 변경을 할 수 없으며, 예약 조회 기능만 사용할 수 있습니다.',
  },
};

export const USER_STATUS_MAP = {
  FORCE_CHANGE_PASSWORD: 'Invited',
  CONFIRMED: 'Active',
};
